<?php

namespace DD\App\FromXmlUpdater;

use DD\App\Factory\RealEstateFromXmlFactory;
use DD\App\Logger;
use DD\App\Repository\RealEstateRepository;
use DD\App\XmlImporter\FileReader;
use DD\App\XmlImporter\PriceHistoryImporter;
use DD\App\XmlImporter\XmlImporter;
use Exception;

class FromXmlUpdater
{
    private XmlImporter $xmlImporter;
    private RealEstateFromXmlFactory $realEstateFromXmlFactory;
    private RealEstateRepository $realEstateRepository;
    private PriceHistoryImporter $priceHistoryImporter;

    public function __construct()
    {
        $this->xmlImporter = new XmlImporter();
        $this->realEstateFromXmlFactory = new RealEstateFromXmlFactory();
        $this->realEstateRepository = new RealEstateRepository();
        $this->priceHistoryImporter = new PriceHistoryImporter(new FileReader());
    }

    public function update(): UpdateResult
    {
        $xmlConvertedToArray = $this->xmlImporter->readXmlFile();
        $realEstatesInDatabase = $this->realEstateRepository->getAllRealEstates();

        $inXml = count($xmlConvertedToArray);
        $updated = 0;
        $skipped = 0;
        $notFoundInDatabase = 0;
        $failed = 0;
        $updatedDetails = [];

        foreach ($xmlConvertedToArray as $realEstate) {
            try {
                $priceHistory = $this->priceHistoryImporter->getPriceHistory($realEstate['id'], $realEstate['price_change_history']);
            } catch (Exception $e) {
                Logger::error('Failed to get price history', $e);

                $failed++;
                continue;
            }

            $newRealEstate = $this->realEstateFromXmlFactory->createRealEstate($realEstate, $priceHistory);

            if (!isset($realEstatesInDatabase[$newRealEstate->externalId])) {
                $notFoundInDatabase++;
                continue;
            }

            $realEstateInDatabase = $realEstatesInDatabase[$newRealEstate->externalId];

            if (
                $realEstateInDatabase->status === $newRealEstate->status
                && $realEstateInDatabase->localNumber === $newRealEstate->localNumber
                && $realEstateInDatabase->cardLinkPdf === $newRealEstate->cardLinkPdf
                && $realEstateInDatabase->price === $newRealEstate->price
                && $realEstateInDatabase->pricePerMeter === $newRealEstate->pricePerMeter
                && $realEstateInDatabase->area === $newRealEstate->area
                && $realEstateInDatabase->priceHistory == $priceHistory
                && $realEstateInDatabase->balconyArea === $newRealEstate->balconyArea
                && $realEstateInDatabase->terraceArea === $newRealEstate->terraceArea
                && $realEstateInDatabase->gardenArea === $newRealEstate->gardenArea
                && $realEstateInDatabase->loggiaArea === $newRealEstate->loggiaArea
                && $realEstateInDatabase->mezzanineArea === $newRealEstate->mezzanineArea
                && $realEstateInDatabase->atticArea === $newRealEstate->atticArea
            ) {
                $skipped++;
                continue;
            }

            $this->realEstateRepository->updateRealEstate(
                $realEstateInDatabase->postId,
                $newRealEstate
            );

            $updated++;
            $updatedDetails[] = [
                'before' => json_encode($realEstateInDatabase),
                'after' => json_encode($newRealEstate),
            ];
        }

        $logInfo = [
            'in_xml' => $inXml,
            'updated' => $updated,
            'skipped' => $skipped,
            'failed' => $failed,
            'not_found_in_database' => $notFoundInDatabase,
            'updated_details' => $updatedDetails,
        ];
        Logger::log('Update from XML. ' . json_encode($logInfo));

        return new UpdateResult($inXml, $updated, $skipped, $failed, $notFoundInDatabase);
    }
}
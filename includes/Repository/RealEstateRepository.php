<?php

namespace DD\App\Repository;

use DD\App\DTO\PriceHistoryDto;
use DD\App\DTO\RealEstate;
use DD\App\Enum\SortDirection;
use DD\App\Enum\SortField;
use DD\App\Enum\Status;
use DD\App\Enum\Type;
use DD\App\Factory\PriceHistoryFactory;
use Exception;
use JsonException;
use WP_Query;
use WP_Term;

class RealEstateRepository
{
    private PriceHistoryFactory $priceHistoryFactory;

    public function __construct()
    {
        $this->priceHistoryFactory = new PriceHistoryFactory();
    }

    /**
     * @return array<int>
     */
    public function getAllRealEstateExternalIds(): array
    {
        $query = new WP_Query([
            'post_type' => [DD_POST_TYPE],
            'posts_per_page' => -1,
        ]);

        if (!$query->have_posts()) {
            return [];
        }

        $externalIds = [];

        while ($query->have_posts()) {
            $query->the_post();
            $postId = get_the_ID();

            $externalIds[] = get_post_meta($postId, DD_EXTERNAL_ID, true);
        }

        return array_map('intval', $externalIds);
    }

    /**
     * @return bool Zwraca false jeżeli import się nie uda. Zwraca true jeżeli import się uda.
     *
     * @throws JsonException
     */
    public function saveRealEstateInDatabase(RealEstate $realEstate): bool
    {
        $metaData = [
            DD_ID => $realEstate->localNumber,
            DD_SQUARE => $realEstate->area,
            DD_PRICE => $realEstate->price,
            DD_PRICE_PER_METER => $realEstate->pricePerMeter,
            DD_EXTERNAL_ID => $realEstate->externalId,
            DD_PDF_LINK => $realEstate->cardLinkPdf,
            DD_STATUS => $realEstate->status->value,
            DD_TYPE => $realEstate->type->value,
            DD_FLOOR => $realEstate->floor,
            DD_ROOMS => $realEstate->rooms,
            DD_BALCONY_AREA => $realEstate->balconyArea,
            DD_TERRACE_AREA => $realEstate->terraceArea,
            DD_GARDEN_AREA => $realEstate->gardenArea,
            DD_LOGGIA_AREA => $realEstate->loggiaArea,
            DD_MEZZANINE_AREA => $realEstate->mezzanineArea,
            DD_ATTIC_AREA => $realEstate->atticArea,
        ];

        $postTitle = $this->generatePostTitle($realEstate);

        $postId = wp_insert_post([
            'post_title' => $postTitle,
            'post_status' => 'publish',
            'post_type' => DD_POST_TYPE,
            'meta_input' => $metaData
        ]);

        $this->saveRealEstatePriceHistory($postId, $realEstate->priceHistory);

        if (!is_int($postId)) {
            return false;
        }

        $facilities = [];
        if ($realEstate->hasBalcony) {
            $foundTerm = get_term_by('slug', 'balkon', DD_FACILITIES);

            if ($foundTerm instanceof WP_Term) {
                $facilities[] = $foundTerm->term_id;
            }
        }

        wp_set_post_terms($postId, $facilities, DD_FACILITIES);

        return true;
    }

    /**
     * @return array<int, RealEstate> Key is External ID in database (ID of XML item)
     */
    public function getAllRealEstates(): array
    {
        global $wpdb;

        $query = $wpdb->prepare("
            SELECT
                posts.ID as id,
                MAX(CASE WHEN posts_meta.meta_key = %s THEN posts_meta.meta_value END) AS status,
                MAX(CASE WHEN posts_meta.meta_key = %s THEN posts_meta.meta_value END) AS property_type,
                MAX(CASE WHEN posts_meta.meta_key = %s THEN posts_meta.meta_value END) AS id_number,
                MAX(CASE WHEN posts_meta.meta_key = %s THEN posts_meta.meta_value END) AS price,
                MAX(CASE WHEN posts_meta.meta_key = %s THEN posts_meta.meta_value END) AS price_per_meter,
                MAX(CASE WHEN posts_meta.meta_key = %s THEN posts_meta.meta_value END) AS area,
                MAX(CASE WHEN posts_meta.meta_key = %s THEN posts_meta.meta_value END) AS floor,
                MAX(CASE WHEN posts_meta.meta_key = %s THEN posts_meta.meta_value END) AS rooms,
                MAX(CASE WHEN posts_meta.meta_key = %s THEN posts_meta.meta_value END) AS external_id,
                MAX(CASE WHEN posts_meta.meta_key = %s THEN posts_meta.meta_value END) AS card_link,
                MAX(CASE WHEN posts_meta.meta_key = %s THEN posts_meta.meta_value END) AS price_history,
                MAX(CASE WHEN posts_meta.meta_key = %s THEN posts_meta.meta_value END) AS balcony_area,
                MAX(CASE WHEN posts_meta.meta_key = %s THEN posts_meta.meta_value END) AS terrace_area,
                MAX(CASE WHEN posts_meta.meta_key = %s THEN posts_meta.meta_value END) AS garden_area,
                MAX(CASE WHEN posts_meta.meta_key = %s THEN posts_meta.meta_value END) AS loggia_area,
                MAX(CASE WHEN posts_meta.meta_key = %s THEN posts_meta.meta_value END) AS mezzanine_area,
                MAX(CASE WHEN posts_meta.meta_key = %s THEN posts_meta.meta_value END) AS attic_area
            FROM {$wpdb->posts} posts
            LEFT JOIN {$wpdb->postmeta} posts_meta ON posts.ID = posts_meta.post_id
            WHERE posts.post_type = %s AND posts.post_status = 'publish'
            GROUP BY posts.ID
            ", DD_STATUS, DD_TYPE, DD_ID, DD_PRICE, DD_PRICE_PER_METER, DD_SQUARE, DD_FLOOR, DD_ROOMS, DD_EXTERNAL_ID, DD_PDF_LINK, DD_PRICE_HISTORY, DD_BALCONY_AREA, DD_TERRACE_AREA, DD_GARDEN_AREA, DD_LOGGIA_AREA, DD_MEZZANINE_AREA, DD_ATTIC_AREA, DD_POST_TYPE);

        $results = $wpdb->get_results($query);

        $realEstates = [];

        foreach ($results as $result) {
            $realEstates[$result->external_id] = new RealEstate(
                (int) $result->external_id,
                $result->id_number,
                (int) $result->floor,
                Type::from($result->property_type),
                Status::from($result->status),
                (float) $result->price,
                pricePerMeter: (float) $result->price_per_meter,
                cardLinkPdf: $result->card_link,
                area: is_numeric($result->area) ? (float) $result->area : null,
                rooms: is_numeric($result->rooms) ? (int) $result->rooms : null,
                postId: (int) $result->id,
                priceHistory: $this->priceHistoryFactory->createPriceHistoryFromJson($result->price_history),
                balconyArea: is_numeric($result->balcony_area) ? (float) $result->balcony_area : null,
                terraceArea: is_numeric($result->terrace_area) ? (float) $result->terrace_area : null,
                gardenArea: is_numeric($result->garden_area) ? (float) $result->garden_area : null,
                loggiaArea: is_numeric($result->loggia_area) ? (float) $result->loggia_area : null,
                mezzanineArea: is_numeric($result->mezzanine_area) ? (float) $result->mezzanine_area : null,
                atticArea: is_numeric($result->attic_area) ? (float) $result->attic_area : null
            );
        }

        return $realEstates;
    }

    /**
     * @return array<int, RealEstate> Key is External ID in database (ID of XML item)
     */
    public function getRealEstatesWithSortingAndFiltering(?SortField $sortField = null, ?SortDirection $sortDirection = null, ?Type $filterType = null): array
    {
        global $wpdb;

        $orderClause = '';
        $orderParams = [];
        if ($sortField && $sortDirection) {
            $sortMetaKey = match ($sortField) {
                SortField::PRICE => DD_PRICE,
                SortField::AREA => DD_SQUARE,
            };

            $orderClause = ' ORDER BY CAST(MAX(CASE WHEN posts_meta.meta_key = %s THEN posts_meta.meta_value END) AS DECIMAL(10,2)) ' . $sortDirection->value;
            $orderParams[] = $sortMetaKey;
        }

        $whereClause = '';
        $whereParams = [];
        if ($filterType) {
            $whereClause = ' AND MAX(CASE WHEN posts_meta.meta_key = %s THEN posts_meta.meta_value END) = %s';
            $whereParams = [DD_TYPE, $filterType->value];
        }

        $baseParams = [DD_STATUS, DD_TYPE, DD_ID, DD_PRICE, DD_PRICE_PER_METER, DD_SQUARE, DD_FLOOR, DD_ROOMS, DD_EXTERNAL_ID, DD_PDF_LINK, DD_PRICE_HISTORY, DD_BALCONY_AREA, DD_TERRACE_AREA, DD_GARDEN_AREA, DD_LOGGIA_AREA, DD_MEZZANINE_AREA, DD_ATTIC_AREA, DD_POST_TYPE];

        $allParams = array_merge($baseParams, $whereParams, $orderParams);

        $query = $wpdb->prepare("
            SELECT
                posts.ID as id,
                MAX(CASE WHEN posts_meta.meta_key = %s THEN posts_meta.meta_value END) AS status,
                MAX(CASE WHEN posts_meta.meta_key = %s THEN posts_meta.meta_value END) AS property_type,
                MAX(CASE WHEN posts_meta.meta_key = %s THEN posts_meta.meta_value END) AS id_number,
                MAX(CASE WHEN posts_meta.meta_key = %s THEN posts_meta.meta_value END) AS price,
                MAX(CASE WHEN posts_meta.meta_key = %s THEN posts_meta.meta_value END) AS price_per_meter,
                MAX(CASE WHEN posts_meta.meta_key = %s THEN posts_meta.meta_value END) AS area,
                MAX(CASE WHEN posts_meta.meta_key = %s THEN posts_meta.meta_value END) AS floor,
                MAX(CASE WHEN posts_meta.meta_key = %s THEN posts_meta.meta_value END) AS rooms,
                MAX(CASE WHEN posts_meta.meta_key = %s THEN posts_meta.meta_value END) AS external_id,
                MAX(CASE WHEN posts_meta.meta_key = %s THEN posts_meta.meta_value END) AS card_link,
                MAX(CASE WHEN posts_meta.meta_key = %s THEN posts_meta.meta_value END) AS price_history,
                MAX(CASE WHEN posts_meta.meta_key = %s THEN posts_meta.meta_value END) AS balcony_area,
                MAX(CASE WHEN posts_meta.meta_key = %s THEN posts_meta.meta_value END) AS terrace_area,
                MAX(CASE WHEN posts_meta.meta_key = %s THEN posts_meta.meta_value END) AS garden_area,
                MAX(CASE WHEN posts_meta.meta_key = %s THEN posts_meta.meta_value END) AS loggia_area,
                MAX(CASE WHEN posts_meta.meta_key = %s THEN posts_meta.meta_value END) AS mezzanine_area,
                MAX(CASE WHEN posts_meta.meta_key = %s THEN posts_meta.meta_value END) AS attic_area
            FROM {$wpdb->posts} posts
            LEFT JOIN {$wpdb->postmeta} posts_meta ON posts.ID = posts_meta.post_id
            WHERE posts.post_type = %s AND posts.post_status = 'publish'
            GROUP BY posts.ID
            HAVING 1=1{$whereClause}{$orderClause}
            ", ...$allParams);

        $results = $wpdb->get_results($query);

        $realEstates = [];

        foreach ($results as $result) {
            $realEstates[$result->external_id] = new RealEstate(
                (int) $result->external_id,
                $result->id_number,
                (int) $result->floor,
                Type::from($result->property_type),
                Status::from($result->status),
                (float) $result->price,
                pricePerMeter: (float) $result->price_per_meter,
                cardLinkPdf: $result->card_link,
                area: is_numeric($result->area) ? (float) $result->area : null,
                rooms: is_numeric($result->rooms) ? (int) $result->rooms : null,
                postId: (int) $result->id,
                priceHistory: $this->priceHistoryFactory->createPriceHistoryFromJson($result->price_history),
                balconyArea: is_numeric($result->balcony_area) ? (float) $result->balcony_area : null,
                terraceArea: is_numeric($result->terrace_area) ? (float) $result->terrace_area : null,
                gardenArea: is_numeric($result->garden_area) ? (float) $result->garden_area : null,
                loggiaArea: is_numeric($result->loggia_area) ? (float) $result->loggia_area : null,
                mezzanineArea: is_numeric($result->mezzanine_area) ? (float) $result->mezzanine_area : null,
                atticArea: is_numeric($result->attic_area) ? (float) $result->attic_area : null
            );
        }

        return $realEstates;
    }

    public function updateRealEstate(int $realEstatePostId, RealEstate $realEstate): void
    {
        wp_update_post([
            'ID' => $realEstatePostId,
            'post_title' => $this->generatePostTitle($realEstate),
        ]);
        update_post_meta($realEstatePostId, DD_ID, $realEstate->localNumber);
        update_post_meta($realEstatePostId, DD_STATUS, $realEstate->status->value);
        update_post_meta($realEstatePostId, DD_PDF_LINK, $realEstate->cardLinkPdf);
        update_post_meta($realEstatePostId, DD_PRICE, $realEstate->price);
        update_post_meta($realEstatePostId, DD_PRICE_PER_METER, $realEstate->pricePerMeter);
        update_post_meta($realEstatePostId, DD_SQUARE, $realEstate->area);
        update_post_meta($realEstatePostId, DD_BALCONY_AREA, $realEstate->balconyArea);
        update_post_meta($realEstatePostId, DD_TERRACE_AREA, $realEstate->terraceArea);
        update_post_meta($realEstatePostId, DD_GARDEN_AREA, $realEstate->gardenArea);
        update_post_meta($realEstatePostId, DD_LOGGIA_AREA, $realEstate->loggiaArea);
        update_post_meta($realEstatePostId, DD_MEZZANINE_AREA, $realEstate->mezzanineArea);
        update_post_meta($realEstatePostId, DD_ATTIC_AREA, $realEstate->atticArea);
        $this->saveRealEstatePriceHistory($realEstatePostId, $realEstate->priceHistory);
    }

    public function getRealEstateByPostId(int $postId): ?RealEstate
    {
        global $wpdb;

        $query = $wpdb->prepare("
            SELECT
                posts.ID as id,
                MAX(CASE WHEN posts_meta.meta_key = %s THEN posts_meta.meta_value END) AS status,
                MAX(CASE WHEN posts_meta.meta_key = %s THEN posts_meta.meta_value END) AS property_type,
                MAX(CASE WHEN posts_meta.meta_key = %s THEN posts_meta.meta_value END) AS id_number,
                MAX(CASE WHEN posts_meta.meta_key = %s THEN posts_meta.meta_value END) AS price,
                MAX(CASE WHEN posts_meta.meta_key = %s THEN posts_meta.meta_value END) AS price_per_meter,
                MAX(CASE WHEN posts_meta.meta_key = %s THEN posts_meta.meta_value END) AS area,
                MAX(CASE WHEN posts_meta.meta_key = %s THEN posts_meta.meta_value END) AS floor,
                MAX(CASE WHEN posts_meta.meta_key = %s THEN posts_meta.meta_value END) AS rooms,
                MAX(CASE WHEN posts_meta.meta_key = %s THEN posts_meta.meta_value END) AS external_id,
                MAX(CASE WHEN posts_meta.meta_key = %s THEN posts_meta.meta_value END) AS card_link,
                MAX(CASE WHEN posts_meta.meta_key = %s THEN posts_meta.meta_value END) AS price_history,
                MAX(CASE WHEN posts_meta.meta_key = %s THEN posts_meta.meta_value END) AS balcony_area,
                MAX(CASE WHEN posts_meta.meta_key = %s THEN posts_meta.meta_value END) AS terrace_area,
                MAX(CASE WHEN posts_meta.meta_key = %s THEN posts_meta.meta_value END) AS garden_area,
                MAX(CASE WHEN posts_meta.meta_key = %s THEN posts_meta.meta_value END) AS loggia_area,
                MAX(CASE WHEN posts_meta.meta_key = %s THEN posts_meta.meta_value END) AS mezzanine_area,
                MAX(CASE WHEN posts_meta.meta_key = %s THEN posts_meta.meta_value END) AS attic_area
            FROM {$wpdb->posts} posts
            LEFT JOIN {$wpdb->postmeta} posts_meta ON posts.ID = posts_meta.post_id
            WHERE posts.post_type = %s AND posts.post_status = 'publish' AND posts.ID = %d
            GROUP BY posts.ID
            ", DD_STATUS, DD_TYPE, DD_ID, DD_PRICE, DD_PRICE_PER_METER, DD_SQUARE, DD_FLOOR, DD_ROOMS, DD_EXTERNAL_ID, DD_PDF_LINK, DD_PRICE_HISTORY, DD_BALCONY_AREA, DD_TERRACE_AREA, DD_GARDEN_AREA, DD_LOGGIA_AREA, DD_MEZZANINE_AREA, DD_ATTIC_AREA, DD_POST_TYPE, $postId);

        $results = $wpdb->get_results($query);

        if (empty($results)) {
            return null;
        }

        $result = current($results);

        return new RealEstate(
            (int) $result->external_id,
            $result->id_number,
            (int) $result->floor,
            Type::from($result->property_type),
            Status::from($result->status),
            (float) $result->price,
            pricePerMeter: (float) $result->price_per_meter,
            cardLinkPdf: $result->card_link,
            area: is_numeric($result->area) ? (float) $result->area : null,
            rooms: is_numeric($result->rooms) ? (int) $result->rooms : null,
            postId: (int) $result->id,
            priceHistory: $this->priceHistoryFactory->createPriceHistoryFromJson($result->price_history),
            balconyArea: is_numeric($result->balcony_area) ? (float) $result->balcony_area : null,
            terraceArea: is_numeric($result->terrace_area) ? (float) $result->terrace_area : null,
            gardenArea: is_numeric($result->garden_area) ? (float) $result->garden_area : null,
            loggiaArea: is_numeric($result->loggia_area) ? (float) $result->loggia_area : null,
            mezzanineArea: is_numeric($result->mezzanine_area) ? (float) $result->mezzanine_area : null,
            atticArea: is_numeric($result->attic_area) ? (float) $result->attic_area : null
        );
    }

    private function generatePostTitle(RealEstate $realEstate): string
    {
        $postTitle = ucfirst(DD_TYPES[$realEstate->type->value] ?? throw new Exception("Type {$realEstate->type->value} not found")) . ' ' . $realEstate->localNumber;
        return $postTitle;
    }

    /**
     * @param array<PriceHistoryDto> $priceHistory
     */
    private function saveRealEstatePriceHistory(int $realEstatePostId, array $priceHistory): void
    {
        update_post_meta($realEstatePostId, DD_PRICE_HISTORY, json_encode($priceHistory, JSON_THROW_ON_ERROR));
    }
}